<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>TenantDB.Example</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\TenantDB.Core\TenantDB.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.8" />
  </ItemGroup>

</Project>
