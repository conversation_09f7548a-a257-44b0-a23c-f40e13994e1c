-- Enums for the event sourcing system
-- Based on the XML diagram requirements

-- Source types for event sources
CREATE TYPE source_type AS ENUM (
    'user',
    'compliance_service',
    'scraper',
    'aggregator',
    'campaign',
    'sms',
    'email',
    'api',
    'system'
);

-- Entity types for aggregates
CREATE TYPE entity_type AS ENUM (
    'person',
    'company',
    'website',
    'place'
);

-- Operations for events
CREATE TYPE operation_type AS ENUM (
    'create',
    'update',
    'start',
    'pause',
    'stop',
    'complete',
    'processed',
    'delete'
);

-- Compliance types
CREATE TYPE compliance_type AS ENUM (
    'opt_in',
    'opt_out'
);

-- Compliance methods
CREATE TYPE compliance_method AS ENUM (
    'email',
    'form',
    'phone',
    'letter',
    'website',
    'api'
);

-- Consent status
CREATE TYPE consent_status AS ENUM (
    'unknown',
    'opted_in',
    'opted_out',
    'pending',
    'expired'
);
