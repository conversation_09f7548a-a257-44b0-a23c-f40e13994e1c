-- Utility functions for event sourcing

-- Function to get the current version of an aggregate
CREATE OR REPLACE FUNCTION get_aggregate_version(p_aggregate_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    current_version INTEGER;
BEGIN
    SELECT COALESCE(MAX(version), 0)
    INTO current_version
    FROM base_event
    WHERE aggregate_id = p_aggregate_id;
    RETURN current_version;
END;
$$ LANGUAGE plpgsql;

-- Function to get all events for an aggregate
CREATE OR REPLACE FUNCTION get_aggregate_events(p_aggregate_id INTEGER)
RETURNS TABLE (
    event_id INTEGER,
    event_type VARCHAR,
    version INTEGER,
    event_data JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        be.event_id,
        be.event_type,
        be.version,
        be.event_data,
        be.metadata,
        be.created_at
    FROM base_event be
    WHERE be.aggregate_id = p_aggregate_id
    ORDER BY be.version ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to rebuild an aggregate from events
CREATE OR REPLACE FUNCTION rebuild_aggregate(p_aggregate_id INTEGER)
RETURNS JSONB AS $$
DECLARE
    aggregate_data JSONB := '{}';
    event_record RECORD;
BEGIN
    -- Get all events for this aggregate in order
    FOR event_record IN
        SELECT event_type, event_data, version
        FROM base_event
        WHERE aggregate_id = p_aggregate_id
        ORDER BY version ASC
    LOOP
        -- Apply each event to build the aggregate state
        -- This is a simplified version - in practice you'd have event-specific logic
        aggregate_data := aggregate_data || event_record.event_data;
    END LOOP;
    RETURN aggregate_data;
END;
$$ LANGUAGE plpgsql;

-- Function to add an event (with version checking)
CREATE OR REPLACE FUNCTION add_event(
    p_event_type VARCHAR,
    p_aggregate_id INTEGER,
    p_aggregate_type VARCHAR,
    p_event_data JSONB,
    p_metadata JSONB DEFAULT '{}',
    p_source_id INTEGER DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    new_version INTEGER;
    new_event_id INTEGER;
    default_source_id INTEGER;
BEGIN
    -- Get the next version for this aggregate
    new_version := get_aggregate_version(p_aggregate_id) + 1;
    -- Get default source if not provided
    IF p_source_id IS NULL THEN
        SELECT source_id INTO default_source_id
        FROM event_source
        WHERE source_name = 'system'
        LIMIT 1;
        IF default_source_id IS NULL THEN
            RAISE EXCEPTION 'No source_id provided and no system source found';
        END IF;
        p_source_id := default_source_id;
    END IF;
    -- Insert the event
    INSERT INTO base_event (
        event_type,
        aggregate_id,
        aggregate_type,
        version,
        event_data,
        metadata,
        source_id
    ) VALUES (
        p_event_type,
        p_aggregate_id,
        p_aggregate_type,
        new_version,
        p_event_data,
        p_metadata,
        p_source_id
    ) RETURNING event_id INTO new_event_id;
    RETURN new_event_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update aggregate table from events
CREATE OR REPLACE FUNCTION update_aggregate_from_events(p_aggregate_id INTEGER)
RETURNS VOID AS $$
DECLARE
    latest_version INTEGER;
    aggregate_data JSONB;
    aggregate_type_val entity_type;
BEGIN
    -- Get the latest version and rebuild data
    SELECT MAX(version), aggregate_type
    INTO latest_version, aggregate_type_val
    FROM base_event
    WHERE aggregate_id = p_aggregate_id
    GROUP BY aggregate_type;
    IF latest_version IS NULL THEN
        RETURN; -- No events for this aggregate
    END IF;
    -- Rebuild the aggregate data
    aggregate_data := rebuild_aggregate(p_aggregate_id);
    -- Update or insert the aggregate
    INSERT INTO aggregate (id, entity_type, data, version, last_updated)
    VALUES (p_aggregate_id, aggregate_type_val, aggregate_data, latest_version, CURRENT_TIMESTAMP)
    ON CONFLICT (id) DO UPDATE SET
        data = EXCLUDED.data,
        version = EXCLUDED.version,
        last_updated = EXCLUDED.last_updated;
END;
$$ LANGUAGE plpgsql;
