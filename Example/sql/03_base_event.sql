-- Base Event table
-- Core event store for all events in the system

CREATE TABLE base_event (
    event_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL,
    aggregate_id UUID NOT NULL,
    aggregate_type VARCHAR(50) NOT NULL,
    version INTEGER NOT NULL,
    event_data JSONB NOT NULL DEFAULT '{}',
    metadata JSONB NOT NULL DEFAULT '{}',
    source_id UUID NOT NULL REFERENCES event_source(source_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT base_event_version_positive CHECK (version > 0),
    CONSTRAINT base_event_data_valid CHECK (jsonb_typeof(event_data) = 'object'),
    CONSTRAINT base_event_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    CONSTRAINT base_event_aggregate_version_unique UNIQUE (aggregate_id, version)
);

-- Indexes for performance
CREATE INDEX idx_base_event_aggregate_id ON base_event(aggregate_id);
CREATE INDEX idx_base_event_aggregate_type ON base_event(aggregate_type);
CREATE INDEX idx_base_event_type ON base_event(event_type);
CREATE INDEX idx_base_event_created_at ON base_event(created_at);
CREATE INDEX idx_base_event_source_id ON base_event(source_id);
CREATE INDEX idx_base_event_data_gin ON base_event USING GIN (event_data);
CREATE INDEX idx_base_event_metadata_gin ON base_event USING GIN (metadata);

-- Composite index for aggregate event ordering
CREATE INDEX idx_base_event_aggregate_version ON base_event(aggregate_id, version);

-- Comments for documentation
COMMENT ON TABLE base_event IS 'Core event store containing all events in the system';
COMMENT ON COLUMN base_event.event_id IS 'Unique identifier for the event';
COMMENT ON COLUMN base_event.event_type IS 'Type/name of the event (e.g., PersonCreated, CompanyUpdated)';
COMMENT ON COLUMN base_event.aggregate_id IS 'ID of the aggregate this event belongs to';
COMMENT ON COLUMN base_event.aggregate_type IS 'Type of aggregate (person, company, etc.)';
COMMENT ON COLUMN base_event.version IS 'Version number of the aggregate after this event';
COMMENT ON COLUMN base_event.event_data IS 'Event-specific data payload';
COMMENT ON COLUMN base_event.metadata IS 'Additional metadata about the event';
COMMENT ON COLUMN base_event.source_id IS 'Reference to the source that generated this event';
