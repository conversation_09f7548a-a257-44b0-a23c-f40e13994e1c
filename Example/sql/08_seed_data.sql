
-- These are ai generated testing values
INSERT INTO event_source (source_name, source_type, description, configuration) VALUES
('system', 'system', 'System-generated events', '{"automated": true}'),
('user_portal', 'user', 'Events from user portal interactions', '{"interface": "web"}'),
('compliance_service', 'compliance_service', 'GDPR compliance service', '{"automated": true, "gdpr_compliant": true}'),
('linkedin_scraper', 'scraper', 'LinkedIn data scraper', '{"platform": "linkedin", "rate_limit": 100}'),
('email_aggregator', 'aggregator', 'Email data aggregation service', '{"sources": ["gmail", "outlook"]}'),
('marketing_campaign', 'campaign', 'Marketing campaign engine', '{"channels": ["email", "sms"]}'),
('sms_service', 'sms', 'SMS notification service', '{"provider": "twilio"}'),
('email_service', 'email', 'Email notification service', '{"provider": "sendgrid"}')
ON CONFLICT (source_name) DO NOTHING;

INSERT INTO aggregate_entity (id, entity_type, origin) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'person', 'linkedin_scraper'),
('550e8400-e29b-41d4-a716-446655440002', 'company', 'linkedin_scraper'),
('550e8400-e29b-41d4-a716-446655440003', 'website', 'web_crawler'),
('550e8400-e29b-41d4-a716-446655440004', 'place', 'google_places')
ON CONFLICT (id) DO NOTHING;
