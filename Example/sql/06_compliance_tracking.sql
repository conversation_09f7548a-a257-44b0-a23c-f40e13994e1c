-- Compliance tracking tables
-- For GDPR and audit requirements

-- Compliance events view (derived from base_event)
CREATE VIEW compliance_event AS
SELECT 
    be.event_id,
    be.aggregate_id,
    be.aggregate_type,
    be.version,
    be.source_id,
    be.created_at,
    (be.event_data->>'compliance_type')::compliance_type AS compliance_type,
    be.event_data->>'entity_id' AS entity_id,
    (be.event_data->>'entity_type')::entity_type AS entity_type,
    (be.event_data->>'operation')::operation_type AS operation,
    (be.event_data->>'method')::compliance_method AS method,
    (be.event_data->>'effective_date')::TIMESTAMP WITH TIME ZONE AS effective_date,
    (be.event_data->>'expiry')::TIMESTAMP WITH TIME ZONE AS expiry,
    be.event_data,
    be.metadata
FROM base_event be
WHERE be.event_type LIKE '%Compliance%';

-- Data events view (derived from base_event)
CREATE VIEW data_event AS
SELECT 
    be.event_id,
    be.aggregate_id,
    be.aggregate_type,
    be.version,
    be.source_id,
    be.created_at,
    (be.event_data->>'entity_type')::entity_type AS entity_type,
    (be.event_data->>'operation')::operation_type AS operation,
    be.event_data,
    be.metadata
FROM base_event be
WHERE be.event_type LIKE '%Data%';

-- Campaign events view (derived from base_event)
CREATE VIEW campaign_event AS
SELECT 
    be.event_id,
    be.aggregate_id,
    be.aggregate_type,
    be.version,
    be.source_id,
    be.created_at,
    (be.event_data->>'campaign_id')::UUID AS campaign_id,
    (be.event_data->>'operation')::operation_type AS operation,
    be.event_data->>'context' AS context,
    be.event_data,
    be.metadata
FROM base_event be
WHERE be.event_type LIKE '%Campaign%';

-- Audit log for compliance tracking
CREATE TABLE audit_log (
    audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_id UUID NOT NULL,
    entity_type entity_type NOT NULL,
    action VARCHAR(100) NOT NULL,
    old_data JSONB,
    new_data JSONB,
    changed_by UUID, -- Reference to user/source
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    reason TEXT,
    
    -- Constraints
    CONSTRAINT audit_log_data_valid CHECK (
        (old_data IS NULL OR jsonb_typeof(old_data) = 'object') AND
        (new_data IS NULL OR jsonb_typeof(new_data) = 'object')
    )
);

-- Indexes for audit log
CREATE INDEX idx_audit_log_entity ON audit_log(entity_id, entity_type);
CREATE INDEX idx_audit_log_changed_at ON audit_log(changed_at);
CREATE INDEX idx_audit_log_changed_by ON audit_log(changed_by);
CREATE INDEX idx_audit_log_action ON audit_log(action);

-- Comments
COMMENT ON TABLE audit_log IS 'Audit trail for compliance and data protection requirements';
COMMENT ON VIEW compliance_event IS 'View of compliance-related events for GDPR tracking';
COMMENT ON VIEW data_event IS 'View of data manipulation events';
COMMENT ON VIEW campaign_event IS 'View of campaign-related events';
