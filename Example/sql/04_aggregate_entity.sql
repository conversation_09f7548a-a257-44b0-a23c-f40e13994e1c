-- Aggregate Entity table
-- Tracks the basic information about aggregates

CREATE TABLE aggregate_entity (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type entity_type NOT NULL,
    origin VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT aggregate_entity_origin_not_empty CHECK (origin IS NULL OR length(trim(origin)) > 0)
);

-- Indexes for performance
CREATE INDEX idx_aggregate_entity_type ON aggregate_entity(entity_type);
CREATE INDEX idx_aggregate_entity_created_at ON aggregate_entity(created_at);
CREATE INDEX idx_aggregate_entity_origin ON aggregate_entity(origin);

-- Comments for documentation
COMMENT ON TABLE aggregate_entity IS 'Basic information about aggregates in the system';
COMMENT ON COLUMN aggregate_entity.id IS 'Unique identifier for the aggregate entity';
COMMENT ON COLUMN aggregate_entity.entity_type IS 'Type of entity (person, company, website, place)';
COMMENT ON COLUMN aggregate_entity.origin IS 'Source or origin of this entity (e.g., scraper name, import batch)';
COMMENT ON COLUMN aggregate_entity.created_at IS 'When this aggregate entity was first created';
