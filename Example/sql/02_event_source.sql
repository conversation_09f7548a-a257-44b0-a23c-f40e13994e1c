-- Event Source table
-- Tracks the sources of events in the system

CREATE TABLE event_source (
    source_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_type source_type NOT NULL,
    source_name VARCHAR(255) NOT NULL,
    description TEXT,
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT event_source_name_unique UNIQUE (source_name),
    CONSTRAINT event_source_config_valid CHECK (jsonb_typeof(configuration) = 'object')
);

-- Indexes for performance
CREATE INDEX idx_event_source_type ON event_source(source_type);
CREATE INDEX idx_event_source_created_at ON event_source(created_at);
CREATE INDEX idx_event_source_config_gin ON event_source USING GIN (configuration);

-- Comments for documentation
COMMENT ON TABLE event_source IS 'Tracks the sources of events in the event sourcing system';
COMMENT ON COLUMN event_source.source_id IS 'Unique identifier for the event source';
COMMENT ON COLUMN event_source.source_type IS 'Type of the event source (user, service, etc.)';
COMMENT ON COLUMN event_source.source_name IS 'Human-readable name for the event source';
COMMENT ON COLUMN event_source.description IS 'Optional description of the event source';
COMMENT ON COLUMN event_source.configuration IS 'JSON configuration specific to the source type';
