using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using TenantDB.Core.Configuration;
using TenantDB.Core.Extensions;
using TenantDB.Core.Services;

namespace TenantDB.Setup;

class Program
{
    static async Task<int> Main(string[] args)
    {
        var builder = Host.CreateApplicationBuilder(args);
        
        // Configure logging
        builder.Logging.ClearProviders();
        builder.Logging.AddConsole();
        builder.Logging.SetMinimumLevel(LogLevel.Information);

        // Add configuration sources
        builder.Configuration.AddCommandLine(args);
        builder.Configuration.AddEnvironmentVariables("TENANTDB_");

        var app = builder.Build();
        var logger = app.Services.GetRequiredService<ILogger<Program>>();

        try
        {
            logger.LogInformation("TenantDB Setup Tool");
            logger.LogInformation("==================");

            // Parse command line arguments
            var config = ParseConfiguration(builder.Configuration, logger);
            if (config == null)
            {
                ShowUsage();
                return 1;
            }

            // Register TenantDB services
            var services = new ServiceCollection();
            services.AddLogging(b => b.AddConsole().SetMinimumLevel(LogLevel.Information));
            services.AddTenantDB(config, "sql");
            
            var serviceProvider = services.BuildServiceProvider();
            var setupService = serviceProvider.GetRequiredService<ITenantDatabaseSetupService>();

            // Get tenant ID from command line or prompt
            var tenantId = builder.Configuration["tenant-id"] ?? builder.Configuration["t"];
            if (string.IsNullOrWhiteSpace(tenantId))
            {
                Console.Write("Enter tenant ID: ");
                tenantId = Console.ReadLine();
                if (string.IsNullOrWhiteSpace(tenantId))
                {
                    logger.LogError("Tenant ID is required");
                    return 1;
                }
            }

            // Check if database already exists
            var exists = await setupService.TenantDatabaseExistsAsync(tenantId);
            if (exists)
            {
                logger.LogWarning("Database for tenant '{TenantId}' already exists", tenantId);
                Console.Write("Do you want to recreate it? (y/N): ");
                var response = Console.ReadLine();
                if (!string.Equals(response, "y", StringComparison.OrdinalIgnoreCase))
                {
                    logger.LogInformation("Setup cancelled");
                    return 0;
                }

                logger.LogInformation("Dropping existing database...");
                await setupService.RemoveTenantDatabaseAsync(tenantId);
            }

            // Create the tenant database
            logger.LogInformation("Creating tenant database for '{TenantId}'...", tenantId);
            var success = await setupService.SetupTenantDatabaseAsync(tenantId);

            if (success)
            {
                logger.LogInformation("✅ Database setup completed successfully!");
                logger.LogInformation("Database name: {DatabaseName}", setupService.GetTenantDatabaseName(tenantId));
                return 0;
            }
            else
            {
                logger.LogError("❌ Database setup failed");
                return 1;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred during setup");
            return 1;
        }
    }

    private static DatabaseConfiguration? ParseConfiguration(IConfiguration configuration, ILogger logger)
    {
        var host = configuration["host"] ?? configuration["h"] ?? "localhost";
        var portStr = configuration["port"] ?? configuration["p"] ?? "5432";
        var database = configuration["database"] ?? configuration["d"] ?? "postgres";
        var username = configuration["username"] ?? configuration["u"];
        var password = configuration["password"] ?? configuration["pw"];

        if (!int.TryParse(portStr, out var port))
        {
            logger.LogError("Invalid port number: {Port}", portStr);
            return null;
        }

        if (string.IsNullOrWhiteSpace(username))
        {
            Console.Write("Enter PostgreSQL username: ");
            username = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(username))
            {
                logger.LogError("Username is required");
                return null;
            }
        }

        if (string.IsNullOrWhiteSpace(password))
        {
            Console.Write("Enter PostgreSQL password: ");
            password = ReadPassword();
            if (string.IsNullOrWhiteSpace(password))
            {
                logger.LogError("Password is required");
                return null;
            }
        }

        return new DatabaseConfiguration
        {
            Host = host,
            Port = port,
            Database = database,
            Username = username,
            Password = password
        };
    }

    private static string ReadPassword()
    {
        var password = "";
        ConsoleKeyInfo key;
        do
        {
            key = Console.ReadKey(true);
            if (key.Key != ConsoleKey.Backspace && key.Key != ConsoleKey.Enter)
            {
                password += key.KeyChar;
                Console.Write("*");
            }
            else if (key.Key == ConsoleKey.Backspace && password.Length > 0)
            {
                password = password[..^1];
                Console.Write("\b \b");
            }
        } while (key.Key != ConsoleKey.Enter);
        Console.WriteLine();
        return password;
    }

    private static void ShowUsage()
    {
        Console.WriteLine();
        Console.WriteLine("Usage: TenantDB.Setup [options]");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("  -h, --host <host>           PostgreSQL host (default: localhost)");
        Console.WriteLine("  -p, --port <port>           PostgreSQL port (default: 5432)");
        Console.WriteLine("  -d, --database <database>   PostgreSQL database (default: postgres)");
        Console.WriteLine("  -u, --username <username>   PostgreSQL username");
        Console.WriteLine("  --pw, --password <password> PostgreSQL password");
        Console.WriteLine("  -t, --tenant-id <id>        Tenant ID for database creation");
        Console.WriteLine();
        Console.WriteLine("Environment variables (prefix with TENANTDB_):");
        Console.WriteLine("  TENANTDB_HOST, TENANTDB_PORT, TENANTDB_DATABASE");
        Console.WriteLine("  TENANTDB_USERNAME, TENANTDB_PASSWORD, TENANTDB_TENANT_ID");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  TenantDB.Setup --host localhost --username postgres --tenant-id my-tenant");
        Console.WriteLine("  TENANTDB_USERNAME=postgres TENANTDB_PASSWORD=secret TenantDB.Setup -t my-tenant");
    }
}
