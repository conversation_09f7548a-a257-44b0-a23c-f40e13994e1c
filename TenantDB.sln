﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TenantDB.Core", "TenantDB.Core\TenantDB.Core.csproj", "{C473A9E2-71A4-499A-B759-5D5771E8E135}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TenantDB.Tests", "TenantDB.Tests\TenantDB.Tests.csproj", "{5881F532-2C54-4370-9903-0433E1CEAE4E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TenantDB.Setup", "TenantDB.Setup\TenantDB.Setup.csproj", "{3BC65013-2FFC-481D-BC11-B65C3183F477}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C473A9E2-71A4-499A-B759-5D5771E8E135}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C473A9E2-71A4-499A-B759-5D5771E8E135}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C473A9E2-71A4-499A-B759-5D5771E8E135}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C473A9E2-71A4-499A-B759-5D5771E8E135}.Release|Any CPU.Build.0 = Release|Any CPU
		{5881F532-2C54-4370-9903-0433E1CEAE4E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5881F532-2C54-4370-9903-0433E1CEAE4E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5881F532-2C54-4370-9903-0433E1CEAE4E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5881F532-2C54-4370-9903-0433E1CEAE4E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3BC65013-2FFC-481D-BC11-B65C3183F477}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3BC65013-2FFC-481D-BC11-B65C3183F477}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3BC65013-2FFC-481D-BC11-B65C3183F477}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3BC65013-2FFC-481D-BC11-B65C3183F477}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
