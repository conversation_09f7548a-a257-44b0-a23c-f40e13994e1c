using FluentAssertions;
using TenantDB.Core.Configuration;

namespace TenantDB.Tests.Configuration;

public class DatabaseConfigurationTests
{
    [Fact]
    public void BuildConnectionString_WithDefaultValues_ShouldReturnValidConnectionString()
    {
        // Arrange
        var config = new DatabaseConfiguration
        {
            Host = "localhost",
            Port = 5432,
            Database = "testdb",
            Username = "testuser",
            Password = "testpass"
        };

        // Act
        var connectionString = config.BuildConnectionString();

        // Assert
        connectionString.Should().Be("Host=localhost;Port=5432;Database=testdb;Username=testuser;Password=testpass;Timeout=30;Command Timeout=30");
    }

    [Fact]
    public void BuildConnectionString_WithSpecificDatabase_ShouldReturnConnectionStringWithSpecifiedDatabase()
    {
        // Arrange
        var config = new DatabaseConfiguration
        {
            Host = "localhost",
            Port = 5432,
            Database = "originaldb",
            Username = "testuser",
            Password = "testpass"
        };

        // Act
        var connectionString = config.BuildConnectionString("newdb");

        // Assert
        connectionString.Should().Be("Host=localhost;Port=5432;Database=newdb;Username=testuser;Password=testpass;Timeout=30;Command Timeout=30");
    }

    [Fact]
    public void BuildConnectionString_WithCustomTimeouts_ShouldIncludeCustomTimeouts()
    {
        // Arrange
        var config = new DatabaseConfiguration
        {
            Host = "localhost",
            Port = 5432,
            Database = "testdb",
            Username = "testuser",
            Password = "testpass",
            ConnectionTimeout = 60,
            CommandTimeout = 120
        };

        // Act
        var connectionString = config.BuildConnectionString();

        // Assert
        connectionString.Should().Be("Host=localhost;Port=5432;Database=testdb;Username=testuser;Password=testpass;Timeout=60;Command Timeout=120");
    }
}
