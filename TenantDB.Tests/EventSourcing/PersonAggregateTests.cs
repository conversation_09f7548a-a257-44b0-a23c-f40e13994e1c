using FluentAssertions;
using TenantDB.Core.EventSourcing.Aggregates;
using TenantDB.Core.EventSourcing.Events;

namespace TenantDB.Tests.EventSourcing;

public class PersonAggregateTests
{
    [Fact]
    public void PersonAggregate_WhenCreated_ShouldHaveCorrectEntityType()
    {
        // Arrange & Act
        var person = new PersonAggregate();

        // Assert
        person.EntityType.Should().Be(TenantDB.Core.EventSourcing.Models.EntityType.Person);
    }

    [Fact]
    public void CreatePerson_WithValidData_ShouldCreateCorrectEvent()
    {
        // Arrange
        var personId = Guid.NewGuid();
        const string firstName = "John";
        const string lastName = "Doe";
        const string email = "<EMAIL>";

        // Act
        var createEvent = PersonAggregate.CreatePerson(personId, firstName, lastName, email);

        // Assert
        createEvent.Should().NotBeNull();
        createEvent.EventType.Should().Be("PersonCreated");
        createEvent.AggregateId.Should().Be(personId);
        createEvent.AggregateType.Should().Be(TenantDB.Core.EventSourcing.Models.EntityType.Person);
        createEvent.Version.Should().Be(1);
        createEvent.Data.FirstName.Should().Be(firstName);
        createEvent.Data.LastName.Should().Be(lastName);
        createEvent.Data.Email.Should().Be(email);
        createEvent.Data.ConsentStatus.Should().Be("unknown");
    }

    [Fact]
    public void Apply_PersonCreatedEvent_ShouldSetCorrectState()
    {
        // Arrange
        var person = new PersonAggregate();
        var personId = Guid.NewGuid();
        var createEvent = new PersonCreatedEvent(personId, "Jane", "Smith", "<EMAIL>", "https://linkedin.com/in/jane")
        {
            Version = 1,
            CreatedAt = DateTime.UtcNow
        };

        // Act
        person.Apply(createEvent);

        // Assert
        person.Id.Should().Be(Guid.Empty); // ID is set separately
        person.FirstName.Should().Be("Jane");
        person.LastName.Should().Be("Smith");
        person.Email.Should().Be("<EMAIL>");
        person.LinkedInUrl.Should().Be("https://linkedin.com/in/jane");
        person.ConsentStatus.Should().Be("unknown");
        person.Version.Should().Be(1);
        person.IsDeleted.Should().BeFalse();
        person.FullName.Should().Be("Jane Smith");
    }

    [Fact]
    public void Apply_PersonUpdatedEvent_ShouldUpdateOnlyProvidedFields()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid() };
        var createEvent = new PersonCreatedEvent(person.Id, "John", "Doe", "<EMAIL>")
        {
            Version = 1,
            CreatedAt = DateTime.UtcNow
        };
        person.Apply(createEvent);

        var updateEvent = new PersonUpdatedEvent(person.Id, new PersonUpdatedEventData
        {
            FirstName = "Jonathan",
            Email = "<EMAIL>"
            // LastName and LinkedInUrl not provided
        })
        {
            Version = 2,
            CreatedAt = DateTime.UtcNow.AddMinutes(1)
        };

        // Act
        person.Apply(updateEvent);

        // Assert
        person.FirstName.Should().Be("Jonathan");
        person.LastName.Should().Be("Doe"); // Unchanged
        person.Email.Should().Be("<EMAIL>");
        person.LinkedInUrl.Should().BeNull(); // Unchanged
        person.Version.Should().Be(2);
    }

    [Fact]
    public void Apply_PersonConsentChangedEvent_ShouldUpdateConsentStatus()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid() };
        var createEvent = new PersonCreatedEvent(person.Id, "John", "Doe")
        {
            Version = 1,
            CreatedAt = DateTime.UtcNow
        };
        person.Apply(createEvent);

        var consentEvent = new PersonConsentChangedEvent(person.Id, "opted_in", "email", "User clicked opt-in link")
        {
            Version = 2,
            CreatedAt = DateTime.UtcNow.AddMinutes(1)
        };

        // Act
        person.Apply(consentEvent);

        // Assert
        person.ConsentStatus.Should().Be("opted_in");
        person.Version.Should().Be(2);
    }

    [Fact]
    public void UpdatePerson_ShouldCreateCorrectEvent()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid(), Version = 1 };
        var updateData = new PersonUpdatedEventData
        {
            FirstName = "Updated",
            Email = "<EMAIL>"
        };

        // Act
        var updateEvent = person.UpdatePerson(updateData);

        // Assert
        updateEvent.Should().NotBeNull();
        updateEvent.EventType.Should().Be("PersonUpdated");
        updateEvent.AggregateId.Should().Be(person.Id);
        updateEvent.Version.Should().Be(2);
        updateEvent.Data.Should().Be(updateData);
    }

    [Fact]
    public void ChangeConsent_ShouldCreateCorrectEvent()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid(), Version = 1 };

        // Act
        var consentEvent = person.ChangeConsent("opted_out", "phone", "User called to opt out");

        // Assert
        consentEvent.Should().NotBeNull();
        consentEvent.EventType.Should().Be("PersonConsentChanged");
        consentEvent.AggregateId.Should().Be(person.Id);
        consentEvent.Version.Should().Be(2);
        consentEvent.Data.ConsentStatus.Should().Be("opted_out");
        consentEvent.Data.Method.Should().Be("phone");
        consentEvent.Data.Reason.Should().Be("User called to opt out");
    }

    [Fact]
    public void CanContact_WhenOptedInAndNotDeleted_ShouldReturnTrue()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid() };
        var createEvent = new PersonCreatedEvent(person.Id, "John", "Doe") { Version = 1 };
        var consentEvent = new PersonConsentChangedEvent(person.Id, "opted_in", "email") { Version = 2 };
        
        person.Apply(createEvent);
        person.Apply(consentEvent);

        // Act & Assert
        person.CanContact().Should().BeTrue();
    }

    [Fact]
    public void CanContact_WhenOptedOut_ShouldReturnFalse()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid() };
        var createEvent = new PersonCreatedEvent(person.Id, "John", "Doe") { Version = 1 };
        var consentEvent = new PersonConsentChangedEvent(person.Id, "opted_out", "email") { Version = 2 };
        
        person.Apply(createEvent);
        person.Apply(consentEvent);

        // Act & Assert
        person.CanContact().Should().BeFalse();
    }

    [Fact]
    public void IsValid_WithFirstAndLastName_ShouldReturnTrue()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid() };
        var createEvent = new PersonCreatedEvent(person.Id, "John", "Doe") { Version = 1 };
        person.Apply(createEvent);

        // Act & Assert
        person.IsValid().Should().BeTrue();
    }

    [Fact]
    public void IsValid_WithoutFirstName_ShouldReturnFalse()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid() };
        var createEvent = new PersonCreatedEvent(person.Id, "", "Doe") { Version = 1 };
        person.Apply(createEvent);

        // Act & Assert
        person.IsValid().Should().BeFalse();
    }

    [Fact]
    public void GetState_ShouldReturnSerializableJsonDocument()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid() };
        var createEvent = new PersonCreatedEvent(person.Id, "John", "Doe", "<EMAIL>") { Version = 1 };
        person.Apply(createEvent);

        // Act
        var state = person.GetState();

        // Assert
        state.Should().NotBeNull();
        var json = state.RootElement.GetRawText();
        json.Should().Contain("John");
        json.Should().Contain("Doe");
        json.Should().Contain("<EMAIL>");
    }

    [Fact]
    public void SetState_ShouldRestoreStateFromJsonDocument()
    {
        // Arrange
        var person = new PersonAggregate { Id = Guid.NewGuid() };
        var originalPerson = new PersonAggregate { Id = Guid.NewGuid() };
        var createEvent = new PersonCreatedEvent(originalPerson.Id, "Jane", "Smith", "<EMAIL>") { Version = 1 };
        originalPerson.Apply(createEvent);
        
        var state = originalPerson.GetState();

        // Act
        person.SetState(state);

        // Assert
        person.FirstName.Should().Be("Jane");
        person.LastName.Should().Be("Smith");
        person.Email.Should().Be("<EMAIL>");
        person.ConsentStatus.Should().Be("unknown");
        person.IsDeleted.Should().BeFalse();
    }
}
