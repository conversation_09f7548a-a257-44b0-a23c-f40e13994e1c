using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Data;
using TenantDB.Core.DataLayer;
using TenantDB.Core.EventSourcing;
using TenantDB.Core.EventSourcing.Aggregates;
using TenantDB.Core.EventSourcing.Events;

namespace TenantDB.Tests.EventSourcing;

public class AggregateRepositoryTests
{
    private readonly Mock<IDataLayer> _mockDataLayer;
    private readonly Mock<IEventStore> _mockEventStore;
    private readonly Mock<IDbConnection> _mockConnection;
    private readonly Mock<ILogger<DapperAggregateRepository>> _mockLogger;
    private readonly DapperAggregateRepository _repository;

    public AggregateRepositoryTests()
    {
        _mockDataLayer = new Mock<IDataLayer>();
        _mockEventStore = new Mock<IEventStore>();
        _mockConnection = new Mock<IDbConnection>();
        _mockLogger = new Mock<ILogger<DapperAggregateRepository>>();
        
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string>()))
            .ReturnsAsync(_mockConnection.Object);
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string?>()))
            .ReturnsAsync(_mockConnection.Object);
            
        _repository = new DapperAggregateRepository(_mockDataLayer.Object, _mockEventStore.Object, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithNullDataLayer_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        var act = () => new DapperAggregateRepository(null!, _mockEventStore.Object, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>().WithParameterName("dataLayer");
    }

    [Fact]
    public void Constructor_WithNullEventStore_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        var act = () => new DapperAggregateRepository(_mockDataLayer.Object, null!, _mockLogger.Object);
        act.Should().Throw<ArgumentNullException>().WithParameterName("eventStore");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        var act = () => new DapperAggregateRepository(_mockDataLayer.Object, _mockEventStore.Object, null!);
        act.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    [Fact]
    public async Task SaveAsync_WithValidAggregate_ShouldCallDataLayerCorrectly()
    {
        // Arrange
        var person = new PersonAggregate
        {
            Id = Guid.NewGuid(),
            Version = 1
        };
        var createEvent = new PersonCreatedEvent(person.Id, "John", "Doe") { Version = 1 };
        person.Apply(createEvent);

        // Act
        await _repository.SaveAsync(person);

        // Assert
        _mockDataLayer.Verify(x => x.GetConnectionAsync(It.IsAny<string?>()), Times.Once);
    }

    [Fact]
    public async Task LoadAsync_ShouldCallDataLayerCorrectly()
    {
        // Arrange
        var aggregateId = Guid.NewGuid();

        // Act
        var person = await _repository.LoadAsync<PersonAggregate>(aggregateId);

        // Assert
        _mockDataLayer.Verify(x => x.GetConnectionAsync(It.IsAny<string?>()), Times.Once);
    }

    [Fact]
    public async Task LoadFromEventsAsync_WithExistingEvents_ShouldRebuildAggregate()
    {
        // Arrange
        var aggregateId = Guid.NewGuid();
        var events = new List<Core.EventSourcing.Models.BaseEvent>
        {
            new PersonCreatedEvent(aggregateId, "John", "Doe", "<EMAIL>") { Version = 1 },
            new PersonUpdatedEvent(aggregateId, new PersonUpdatedEventData { FirstName = "Jonathan" }) { Version = 2 }
        };

        _mockEventStore.Setup(x => x.GetEventsAsync(aggregateId))
            .ReturnsAsync(events);

        // Act
        var person = await _repository.LoadFromEventsAsync<PersonAggregate>(aggregateId);

        // Assert
        person.Should().NotBeNull();
        person!.Id.Should().Be(aggregateId);
        person.FirstName.Should().Be("Jonathan"); // Should reflect the update
        person.LastName.Should().Be("Doe");
        person.Email.Should().Be("<EMAIL>");
        person.Version.Should().Be(2);
    }

    [Fact]
    public async Task LoadFromEventsAsync_WithNoEvents_ShouldReturnNull()
    {
        // Arrange
        var aggregateId = Guid.NewGuid();
        _mockEventStore.Setup(x => x.GetEventsAsync(aggregateId))
            .ReturnsAsync(new List<Core.EventSourcing.Models.BaseEvent>());

        // Act
        var person = await _repository.LoadFromEventsAsync<PersonAggregate>(aggregateId);

        // Assert
        person.Should().BeNull();
    }

    [Fact]
    public async Task ExistsAsync_ShouldCallDataLayerCorrectly()
    {
        // Arrange
        var aggregateId = Guid.NewGuid();

        // Act
        var exists = await _repository.ExistsAsync(aggregateId);

        // Assert
        _mockDataLayer.Verify(x => x.GetConnectionAsync(It.IsAny<string?>()), Times.Once);
    }

    [Fact]
    public async Task GetAllAsync_ShouldCallDataLayerCorrectly()
    {
        // Act
        var aggregates = await _repository.GetAllAsync<PersonAggregate>();

        // Assert
        aggregates.Should().NotBeNull();
        _mockDataLayer.Verify(x => x.GetConnectionAsync(It.IsAny<string?>()), Times.Once);
    }

    [Fact]
    public async Task DeleteAsync_ShouldCallDataLayerCorrectly()
    {
        // Arrange
        var aggregateId = Guid.NewGuid();

        // Act
        var result = await _repository.DeleteAsync(aggregateId);

        // Assert
        _mockDataLayer.Verify(x => x.GetConnectionAsync(It.IsAny<string?>()), Times.Once);
    }

    [Fact]
    public async Task SaveAsync_WhenExceptionOccurs_ShouldReturnFalse()
    {
        // Arrange
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string>()))
            .ThrowsAsync(new Exception("Database error"));
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string?>()))
            .ThrowsAsync(new Exception("Database error"));

        var person = new PersonAggregate { Id = Guid.NewGuid() };

        // Act
        var result = await _repository.SaveAsync(person);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task LoadAsync_WhenExceptionOccurs_ShouldReturnNull()
    {
        // Arrange
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string>()))
            .ThrowsAsync(new Exception("Database error"));
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string?>()))
            .ThrowsAsync(new Exception("Database error"));

        var aggregateId = Guid.NewGuid();

        // Act
        var person = await _repository.LoadAsync<PersonAggregate>(aggregateId);

        // Assert
        person.Should().BeNull();
    }

    [Fact]
    public async Task LoadFromEventsAsync_WhenExceptionOccurs_ShouldReturnNull()
    {
        // Arrange
        _mockEventStore.Setup(x => x.GetEventsAsync(It.IsAny<Guid>()))
            .ThrowsAsync(new Exception("Event store error"));

        var aggregateId = Guid.NewGuid();

        // Act
        var person = await _repository.LoadFromEventsAsync<PersonAggregate>(aggregateId);

        // Assert
        person.Should().BeNull();
    }

    [Fact]
    public async Task ExistsAsync_WhenExceptionOccurs_ShouldReturnFalse()
    {
        // Arrange
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string>()))
            .ThrowsAsync(new Exception("Database error"));
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string?>()))
            .ThrowsAsync(new Exception("Database error"));

        var aggregateId = Guid.NewGuid();

        // Act
        var exists = await _repository.ExistsAsync(aggregateId);

        // Assert
        exists.Should().BeFalse();
    }

    [Fact]
    public async Task GetAllAsync_WhenExceptionOccurs_ShouldReturnEmptyCollection()
    {
        // Arrange
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string>()))
            .ThrowsAsync(new Exception("Database error"));
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string?>()))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var aggregates = await _repository.GetAllAsync<PersonAggregate>();

        // Assert
        aggregates.Should().NotBeNull();
        aggregates.Should().BeEmpty();
    }

    [Fact]
    public async Task DeleteAsync_WhenExceptionOccurs_ShouldReturnFalse()
    {
        // Arrange
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string>()))
            .ThrowsAsync(new Exception("Database error"));
        _mockDataLayer.Setup(x => x.GetConnectionAsync(It.IsAny<string?>()))
            .ThrowsAsync(new Exception("Database error"));

        var aggregateId = Guid.NewGuid();

        // Act
        var result = await _repository.DeleteAsync(aggregateId);

        // Assert
        result.Should().BeFalse();
    }
}
