using FluentAssertions;
using System.Text.Json;
using TenantDB.Core.EventSourcing.Events;

namespace TenantDB.Tests.EventSourcing;

public class PersonEventsTests
{
    [Fact]
    public void PersonCreatedEvent_ShouldHaveCorrectEventType()
    {
        // Arrange & Act
        var @event = new PersonCreatedEvent();

        // Assert
        @event.EventType.Should().Be("PersonCreated");
        @event.AggregateType.Should().Be(TenantDB.Core.EventSourcing.Models.EntityType.Person);
    }

    [Fact]
    public void PersonCreatedEvent_WithConstructorParameters_ShouldSetDataCorrectly()
    {
        // Arrange
        var aggregateId = Guid.NewGuid();
        const string firstName = "John";
        const string lastName = "Doe";
        const string email = "<EMAIL>";
        const string linkedInUrl = "https://linkedin.com/in/john";

        // Act
        var @event = new PersonCreatedEvent(aggregateId, firstName, lastName, email, linkedInUrl);

        // Assert
        @event.AggregateId.Should().Be(aggregateId);
        @event.Data.FirstName.Should().Be(firstName);
        @event.Data.LastName.Should().Be(lastName);
        @event.Data.Email.Should().Be(email);
        @event.Data.LinkedInUrl.Should().Be(linkedInUrl);
        @event.Data.ConsentStatus.Should().Be("unknown");
    }

    [Fact]
    public void PersonCreatedEvent_SerializeEventData_ShouldReturnValidJson()
    {
        // Arrange
        var @event = new PersonCreatedEvent(Guid.NewGuid(), "Jane", "Smith", "<EMAIL>");

        // Act
        var serialized = @event.SerializeEventData();

        // Assert
        serialized.Should().NotBeNull();
        var json = serialized.RootElement.GetRawText();
        json.Should().Contain("Jane");
        json.Should().Contain("Smith");
        json.Should().Contain("<EMAIL>");
    }

    [Fact]
    public void PersonCreatedEvent_DeserializeEventData_ShouldRestoreData()
    {
        // Arrange
        var originalEvent = new PersonCreatedEvent(Guid.NewGuid(), "Bob", "Johnson", "<EMAIL>");
        var serialized = originalEvent.SerializeEventData();
        var newEvent = new PersonCreatedEvent();

        // Act
        newEvent.DeserializeEventData(serialized);

        // Assert
        newEvent.Data.FirstName.Should().Be("Bob");
        newEvent.Data.LastName.Should().Be("Johnson");
        newEvent.Data.Email.Should().Be("<EMAIL>");
        newEvent.Data.ConsentStatus.Should().Be("unknown");
    }

    [Fact]
    public void PersonUpdatedEvent_ShouldHaveCorrectEventType()
    {
        // Arrange & Act
        var @event = new PersonUpdatedEvent();

        // Assert
        @event.EventType.Should().Be("PersonUpdated");
        @event.AggregateType.Should().Be(TenantDB.Core.EventSourcing.Models.EntityType.Person);
    }

    [Fact]
    public void PersonUpdatedEvent_WithUpdateData_ShouldSetDataCorrectly()
    {
        // Arrange
        var aggregateId = Guid.NewGuid();
        var updateData = new PersonUpdatedEventData
        {
            FirstName = "UpdatedFirst",
            LastName = "UpdatedLast",
            Email = "<EMAIL>",
            ConsentStatus = "opted_in"
        };

        // Act
        var @event = new PersonUpdatedEvent(aggregateId, updateData);

        // Assert
        @event.AggregateId.Should().Be(aggregateId);
        @event.Data.Should().Be(updateData);
        @event.Data.FirstName.Should().Be("UpdatedFirst");
        @event.Data.LastName.Should().Be("UpdatedLast");
        @event.Data.Email.Should().Be("<EMAIL>");
        @event.Data.ConsentStatus.Should().Be("opted_in");
    }

    [Fact]
    public void PersonConsentChangedEvent_ShouldHaveCorrectEventType()
    {
        // Arrange & Act
        var @event = new PersonConsentChangedEvent();

        // Assert
        @event.EventType.Should().Be("PersonConsentChanged");
        @event.AggregateType.Should().Be(TenantDB.Core.EventSourcing.Models.EntityType.Person);
    }

    [Fact]
    public void PersonConsentChangedEvent_WithConstructorParameters_ShouldSetDataCorrectly()
    {
        // Arrange
        var aggregateId = Guid.NewGuid();
        const string consentStatus = "opted_out";
        const string method = "phone";
        const string reason = "User requested removal";

        // Act
        var @event = new PersonConsentChangedEvent(aggregateId, consentStatus, method, reason);

        // Assert
        @event.AggregateId.Should().Be(aggregateId);
        @event.Data.ConsentStatus.Should().Be(consentStatus);
        @event.Data.Method.Should().Be(method);
        @event.Data.Reason.Should().Be(reason);
    }

    [Fact]
    public void PersonConsentChangedEvent_SerializeAndDeserialize_ShouldPreserveData()
    {
        // Arrange
        var originalEvent = new PersonConsentChangedEvent(Guid.NewGuid(), "opted_in", "email", "User clicked link");
        var serialized = originalEvent.SerializeEventData();
        var newEvent = new PersonConsentChangedEvent();

        // Act
        newEvent.DeserializeEventData(serialized);

        // Assert
        newEvent.Data.ConsentStatus.Should().Be("opted_in");
        newEvent.Data.Method.Should().Be("email");
        newEvent.Data.Reason.Should().Be("User clicked link");
    }

    [Fact]
    public void BaseEvent_ShouldHaveUniqueEventId()
    {
        // Arrange & Act
        var event1 = new PersonCreatedEvent();
        var event2 = new PersonCreatedEvent();

        // Assert
        event1.EventId.Should().NotBe(Guid.Empty);
        event2.EventId.Should().NotBe(Guid.Empty);
        event1.EventId.Should().NotBe(event2.EventId);
    }

    [Fact]
    public void BaseEvent_ShouldHaveCreatedAtSet()
    {
        // Arrange
        var beforeCreation = DateTime.UtcNow;

        // Act
        var @event = new PersonCreatedEvent();
        var afterCreation = DateTime.UtcNow;

        // Assert
        @event.CreatedAt.Should().BeAfter(beforeCreation.AddSeconds(-1));
        @event.CreatedAt.Should().BeBefore(afterCreation.AddSeconds(1));
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void PersonCreatedEventData_WithInvalidFirstName_ShouldStillCreate(string? firstName)
    {
        // Arrange & Act
        var eventData = new PersonCreatedEventData
        {
            FirstName = firstName ?? string.Empty,
            LastName = "Doe"
        };

        // Assert
        eventData.Should().NotBeNull();
        eventData.FirstName.Should().Be(firstName ?? string.Empty);
    }

    [Fact]
    public void PersonUpdatedEventData_WithNullValues_ShouldAllowNulls()
    {
        // Arrange & Act
        var updateData = new PersonUpdatedEventData
        {
            FirstName = null,
            LastName = null,
            Email = null,
            LinkedInUrl = null,
            ConsentStatus = null
        };

        // Assert
        updateData.Should().NotBeNull();
        updateData.FirstName.Should().BeNull();
        updateData.LastName.Should().BeNull();
        updateData.Email.Should().BeNull();
        updateData.LinkedInUrl.Should().BeNull();
        updateData.ConsentStatus.Should().BeNull();
    }
}
