# Extending the Event Sourcing System

This document explains how to extend the TenantDB event sourcing system with new event types and aggregates.

## Overview

The event sourcing system is built around three main concepts:
- **Events**: Immutable records of what happened
- **Aggregates**: Current state built from events
- **Event Store**: Persistence layer for events

## Creating New Events

### 1. Define Event Data

First, create a class to hold your event data:

```csharp
public class CompanyCreatedEventData
{
    public string Name { get; set; } = string.Empty;
    public string? Domain { get; set; }
    public string? Identifier { get; set; }
    public string ConsentStatus { get; set; } = "unknown";
}
```

### 2. Create the Event Class

Extend `BaseEvent<T>` with your event data:

```csharp
public class CompanyCreatedEvent : BaseEvent<CompanyCreatedEventData>
{
    public override string EventType => "CompanyCreated";

    public CompanyCreatedEvent()
    {
        AggregateType = "company";
    }

    public CompanyCreatedEvent(Guid aggregateId, string name, string? domain = null, string? identifier = null)
        : this()
    {
        AggregateId = aggregateId;
        Data = new CompanyCreatedEventData
        {
            Name = name,
            Domain = domain,
            Identifier = identifier
        };
    }
}
```

### 3. Event Naming Conventions

- Event types should be in PascalCase (e.g., "CompanyCreated", "PersonUpdated")
- Event classes should end with "Event" (e.g., `CompanyCreatedEvent`)
- Event data classes should end with "EventData" (e.g., `CompanyCreatedEventData`)

## Creating New Aggregates

### 1. Define Aggregate State

Create a state class to hold the current state:

```csharp
public class CompanyState
{
    public string Name { get; set; } = string.Empty;
    public string? Domain { get; set; }
    public string? Identifier { get; set; }
    public string ConsentStatus { get; set; } = "unknown";
    public bool IsDeleted { get; set; }
}
```

### 2. Create the Aggregate Class

Extend `Aggregate<T>` with your state:

```csharp
public class CompanyAggregate : Aggregate<CompanyState>
{
    public override EntityType EntityType => EntityType.Company;

    // Expose state properties
    public string Name => State.Name;
    public string? Domain => State.Domain;
    public string? Identifier => State.Identifier;
    public string ConsentStatus => State.ConsentStatus;
    public bool IsDeleted => State.IsDeleted;

    // Apply events
    public override void Apply<TEventData>(BaseEvent<TEventData> @event)
    {
        switch (@event)
        {
            case CompanyCreatedEvent companyCreated:
                Apply(companyCreated);
                break;
            case CompanyUpdatedEvent companyUpdated:
                Apply(companyUpdated);
                break;
            default:
                ApplyUntyped(@event);
                break;
        }
    }

    public void Apply(CompanyCreatedEvent @event)
    {
        State.Name = @event.Data.Name;
        State.Domain = @event.Data.Domain;
        State.Identifier = @event.Data.Identifier;
        State.ConsentStatus = @event.Data.ConsentStatus;
        State.IsDeleted = false;

        Version = @event.Version;
        LastUpdated = @event.CreatedAt;
        
        if (Version == 1)
        {
            CreatedAt = @event.CreatedAt;
        }
    }

    // Factory methods
    public static CompanyCreatedEvent CreateCompany(Guid? companyId, string name, string? domain = null)
    {
        var id = companyId ?? Guid.NewGuid();
        return new CompanyCreatedEvent(id, name, domain)
        {
            Version = 1
        };
    }

    // Business logic methods
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(Name);
    }
}
```

## Working with Events and Aggregates

### 1. Creating and Saving Events

```csharp
// Get services
var eventStore = serviceProvider.GetRequiredService<IEventStore>();
var aggregateRepo = serviceProvider.GetRequiredService<IAggregateRepository>();

// Create an event source if needed
var eventSource = await eventStore.GetEventSourceByNameAsync("api") 
    ?? await eventStore.CreateEventSourceAsync("api", SourceType.Api);

// Create a new company
var companyId = Guid.NewGuid();
var createEvent = CompanyAggregate.CreateCompany(companyId, "Acme Corp", "acme.com");
createEvent.SourceId = eventSource.SourceId;

// Save the event
await eventStore.SaveEventAsync(createEvent, "tenant_my_tenant");

// Create and save the aggregate
var company = new CompanyAggregate { Id = companyId };
company.Apply(createEvent);
await aggregateRepo.SaveAsync(company, "tenant_my_tenant");
```

### 2. Loading and Updating Aggregates

```csharp
// Load aggregate from repository (current state)
var company = await aggregateRepo.LoadAsync<CompanyAggregate>(companyId, "tenant_my_tenant");

// Or load from events (rebuild from event history)
var companyFromEvents = await aggregateRepo.LoadFromEventsAsync<CompanyAggregate>(companyId, "tenant_my_tenant");

// Update the company
if (company != null)
{
    var updateEvent = new CompanyUpdatedEvent(companyId, new CompanyUpdatedEventData 
    { 
        Name = "Acme Corporation" 
    })
    {
        Version = company.Version + 1,
        SourceId = eventSource.SourceId
    };

    await eventStore.SaveEventAsync(updateEvent, "tenant_my_tenant");
    
    company.Apply(updateEvent);
    await aggregateRepo.SaveAsync(company, "tenant_my_tenant");
}
```

### 3. Querying Events

```csharp
// Get all events for an aggregate
var events = await eventStore.GetEventsAsync(companyId, "tenant_my_tenant");

// Get events from a specific version
var recentEvents = await eventStore.GetEventsFromVersionAsync(companyId, 5, "tenant_my_tenant");

// Check aggregate version
var currentVersion = await eventStore.GetAggregateVersionAsync(companyId, "tenant_my_tenant");
```

## Best Practices

### 1. Event Design
- Events should be immutable and represent facts
- Include all necessary data in the event (don't rely on external lookups)
- Use meaningful event names that describe business actions
- Keep event data focused and minimal

### 2. Aggregate Design
- Aggregates should enforce business rules
- Keep aggregates small and focused
- Use factory methods for creating new aggregates
- Implement validation methods

### 3. Versioning
- Events are immutable - never change existing event structures
- Create new event types for schema changes
- Handle old event versions in your Apply methods
- Use semantic versioning for your event schemas

### 4. Error Handling
- Validate data before creating events
- Handle missing or invalid events gracefully
- Log errors but don't throw exceptions in Apply methods
- Use compensation events for corrections

## Example: Complete Person Implementation

See `TenantDB.Core/EventSourcing/Events/PersonEvents.cs` and `TenantDB.Core/EventSourcing/Aggregates/PersonAggregate.cs` for a complete implementation example that demonstrates:

- Multiple event types (Created, Updated, ConsentChanged)
- Proper state management
- Business logic methods
- Factory methods for event creation
- Validation and business rules

This serves as a template for implementing your own domain-specific events and aggregates.
