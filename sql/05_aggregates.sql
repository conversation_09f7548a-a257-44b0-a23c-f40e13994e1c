-- Aggregate table
-- Current state of aggregates built from events

CREATE TABLE aggregate (
    id UUID PRIMARY KEY REFERENCES aggregate_entity(id),
    entity_type entity_type NOT NULL,
    data JSONB NOT NULL DEFAULT '{}',
    metadata JSONB NOT NULL DEFAULT '{}',
    version INTEGER NOT NULL DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT aggregate_version_non_negative CHECK (version >= 0),
    CONSTRAINT aggregate_data_valid CHECK (jsonb_typeof(data) = 'object'),
    CONSTRAINT aggregate_metadata_valid CHECK (jsonb_typeof(metadata) = 'object')
);

-- Indexes for performance
CREATE INDEX idx_aggregate_type ON aggregate(entity_type);
CREATE INDEX idx_aggregate_last_updated ON aggregate(last_updated);
CREATE INDEX idx_aggregate_created_at ON aggregate(created_at);
CREATE INDEX idx_aggregate_version ON aggregate(version);
CREATE INDEX idx_aggregate_data_gin ON aggregate USING GIN (data);
CREATE INDEX idx_aggregate_metadata_gin ON aggregate USING GIN (metadata);

-- Comments for documentation
COMMENT ON TABLE aggregate IS 'Current state of aggregates reconstructed from events';
COMMENT ON COLUMN aggregate.id IS 'Reference to the aggregate entity';
COMMENT ON COLUMN aggregate.entity_type IS 'Type of entity (person, company, website, place)';
COMMENT ON COLUMN aggregate.data IS 'Current state data of the aggregate';
COMMENT ON COLUMN aggregate.metadata IS 'Metadata about the aggregate';
COMMENT ON COLUMN aggregate.version IS 'Current version number (matches latest event version)';
COMMENT ON COLUMN aggregate.last_updated IS 'When this aggregate was last updated';
COMMENT ON COLUMN aggregate.created_at IS 'When this aggregate was first created';

-- Specific aggregate views for each entity type
-- These views extract common fields from the JSONB data

-- Company aggregate view
CREATE VIEW company_aggregate AS
SELECT 
    a.id,
    a.data,
    a.metadata,
    a.version,
    a.last_updated,
    a.created_at,
    (a.data->>'consent_status')::consent_status AS consent_status,
    a.data->>'identifier' AS identifier,
    a.data->>'name' AS name,
    a.data->>'domain' AS domain
FROM aggregate a
WHERE a.entity_type = 'company';

-- Person aggregate view
CREATE VIEW person_aggregate AS
SELECT 
    a.id,
    a.data,
    a.metadata,
    a.version,
    a.last_updated,
    a.created_at,
    (a.data->>'consent_status')::consent_status AS consent_status,
    a.data->>'linkedin_url' AS linkedin_url,
    a.data->>'first_name' AS first_name,
    a.data->>'last_name' AS last_name,
    a.data->>'email' AS email
FROM aggregate a
WHERE a.entity_type = 'person';

-- Website aggregate view
CREATE VIEW website_aggregate AS
SELECT 
    a.id,
    a.data,
    a.metadata,
    a.version,
    a.last_updated,
    a.created_at,
    (a.data->>'consent_status')::consent_status AS consent_status,
    a.data->>'domain' AS domain,
    a.data->>'url' AS url
FROM aggregate a
WHERE a.entity_type = 'website';

-- Place aggregate view
CREATE VIEW place_aggregate AS
SELECT 
    a.id,
    a.data,
    a.metadata,
    a.version,
    a.last_updated,
    a.created_at,
    (a.data->>'consent_status')::consent_status AS consent_status,
    a.data->>'identifier' AS identifier,
    a.data->>'name' AS name,
    a.data->>'address' AS address,
    a.data->>'latitude' AS latitude,
    a.data->>'longitude' AS longitude
FROM aggregate a
WHERE a.entity_type = 'place';
