namespace TenantDB.Core.Services;

/// <summary>
/// Service interface for setting up tenant databases
/// </summary>
public interface ITenantDatabaseSetupService
{
    /// <summary>
    /// Sets up a complete database for a new tenant
    /// </summary>
    /// <param name="tenantId">The unique identifier for the tenant</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the setup was successful, false otherwise</returns>
    Task<bool> SetupTenantDatabaseAsync(string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a tenant database exists
    /// </summary>
    /// <param name="tenantId">The unique identifier for the tenant</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the tenant database exists, false otherwise</returns>
    Task<bool> TenantDatabaseExistsAsync(string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes a tenant database (use with caution)
    /// </summary>
    /// <param name="tenantId">The unique identifier for the tenant</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the database was removed successfully, false otherwise</returns>
    Task<bool> RemoveTenantDatabaseAsync(string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the database name for a tenant
    /// </summary>
    /// <param name="tenantId">The unique identifier for the tenant</param>
    /// <returns>The database name for the tenant</returns>
    string GetTenantDatabaseName(string tenantId);
}
