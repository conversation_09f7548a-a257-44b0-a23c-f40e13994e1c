using TenantDB.Core.EventSourcing.Models;

namespace TenantDB.Core.EventSourcing.Events;

/// <summary>
/// Event data for person created event
/// </summary>
public class PersonCreatedEventData
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? LinkedInUrl { get; set; }
    public string ConsentStatus { get; set; } = "unknown";
}

/// <summary>
/// Common metadata for person-related events
/// </summary>
public class PersonEventMetadata
{
    public string? PerformedBy { get; set; }
    public string? CorrelationId { get; set; }
    public string? Notes { get; set; }
}

/// <summary>
/// Event fired when a person is created
/// </summary>
public class PersonCreatedEvent : BaseEvent<PersonCreatedEventData, PersonEventMetadata>
{
    public override string EventType => "PersonCreated";

    public PersonCreatedEvent()
    {
        AggregateType = EntityType.Person;
    }

    public PersonCreatedEvent(Guid aggregateId, string firstName, string lastName, string? email = null, string? linkedInUrl = null)
        : this()
    {
        AggregateId = aggregateId;
        Data = new PersonCreatedEventData
        {
            FirstName = firstName,
            LastName = lastName,
            Email = email,
            LinkedInUrl = linkedInUrl
        };
    }
}

/// <summary>
/// Event data for person updated event
/// </summary>
public class PersonUpdatedEventData
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Email { get; set; }
    public string? LinkedInUrl { get; set; }
    public string? ConsentStatus { get; set; }
}

/// <summary>
/// Event fired when a person is updated
/// </summary>
public class PersonUpdatedEvent : BaseEvent<PersonUpdatedEventData, PersonEventMetadata>
{
    public override string EventType => "PersonUpdated";

    public PersonUpdatedEvent()
    {
        AggregateType = EntityType.Person;
    }

    public PersonUpdatedEvent(Guid aggregateId, PersonUpdatedEventData updateData)
        : this()
    {
        AggregateId = aggregateId;
        Data = updateData;
    }
}

/// <summary>
/// Event data for person consent changed event
/// </summary>
public class PersonConsentChangedEventData
{
    public string ConsentStatus { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
    public string? Reason { get; set; }
}

/// <summary>
/// Event fired when a person's consent status changes
/// </summary>
public class PersonConsentChangedEvent : BaseEvent<PersonConsentChangedEventData, PersonEventMetadata>
{
    public override string EventType => "PersonConsentChanged";

    public PersonConsentChangedEvent()
    {
        AggregateType = EntityType.Person;
    }

    public PersonConsentChangedEvent(Guid aggregateId, string consentStatus, string method, string? reason = null)
        : this()
    {
        AggregateId = aggregateId;
        Data = new PersonConsentChangedEventData
        {
            ConsentStatus = consentStatus,
            Method = method,
            Reason = reason
        };
    }
}
