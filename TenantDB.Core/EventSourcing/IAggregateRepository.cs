using TenantDB.Core.EventSourcing.Models;

namespace TenantDB.Core.EventSourcing;

/// <summary>
/// Interface for aggregate repository operations
/// </summary>
public interface IAggregateRepository
{
    /// <summary>
    /// Save an aggregate to the repository
    /// </summary>
    Task<bool> SaveAsync(Aggregate aggregate);

    /// <summary>
    /// Load an aggregate by ID
    /// </summary>
    Task<TAggregate?> LoadAsync<TAggregate>(Guid aggregateId) where TAggregate : Aggregate, new();

    /// <summary>
    /// Load an aggregate from events
    /// </summary>
    Task<TAggregate?> LoadFromEventsAsync<TAggregate>(Guid aggregateId) where TAggregate : Aggregate, new();

    /// <summary>
    /// Check if an aggregate exists
    /// </summary>
    Task<bool> ExistsAsync(Guid aggregateId);

    /// <summary>
    /// Get all aggregates of a specific type
    /// </summary>
    Task<IEnumerable<TAggregate>> GetAllAsync<TAggregate>() where TAggregate : Aggregate, new();

    /// <summary>
    /// Delete an aggregate
    /// </summary>
    Task<bool> DeleteAsync(Guid aggregateId);
}
