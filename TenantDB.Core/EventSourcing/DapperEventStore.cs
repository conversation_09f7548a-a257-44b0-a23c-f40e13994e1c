using System.Text.Json;
using Dapper;
using Microsoft.Extensions.Logging;
using TenantDB.Core.DataLayer;
using TenantDB.Core.EventSourcing.Models;

namespace TenantDB.Core.EventSourcing;

/// <summary>
/// Dapper-based implementation of the event store
/// </summary>
public class DapperEventStore : IEventStore
{
    private readonly IDataLayer _dataLayer;
    private readonly ILogger<DapperEventStore> _logger;

    public DapperEventStore(IDataLayer dataLayer, ILogger<DapperEventStore> logger)
    {
        _dataLayer = dataLayer ?? throw new ArgumentNullException(nameof(dataLayer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<bool> SaveEventAsync(BaseEvent @event)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            // Ensure event data is serialized
            @event.EventData = @event.SerializeEventData();
            @event.Metadata = @event.SerializeMetadata();
            
            const string sql = @"
                INSERT INTO base_event (event_id, event_type, aggregate_id, aggregate_type, version, event_data, metadata, source_id, created_at)
                VALUES (@EventId, @EventType, @AggregateId, @AggregateType, @Version, @EventData::jsonb, @Metadata::jsonb, @SourceId, @CreatedAt)";

            var parameters = new
            {
                @event.EventId,
                @event.EventType,
                @event.AggregateId,
                AggregateType = @event.AggregateType.ToString().ToLowerInvariant(),
                @event.Version,
                EventData = @event.EventData.RootElement.GetRawText(),
                Metadata = @event.Metadata.RootElement.GetRawText(),
                @event.SourceId,
                @event.CreatedAt
            };

            var result = await connection.ExecuteAsync(sql, parameters);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving event {EventType} for aggregate {AggregateId}", @event.EventType, @event.AggregateId);
            return false;
        }
    }

    public async Task<IEnumerable<BaseEvent>> GetEventsAsync(Guid aggregateId)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            const string sql = @"
                SELECT event_id, event_type, aggregate_id, aggregate_type, version, event_data, metadata, source_id, created_at
                FROM base_event 
                WHERE aggregate_id = @AggregateId 
                ORDER BY version ASC";

            var rows = await connection.QueryAsync(sql, new { AggregateId = aggregateId });
            
            var events = new List<BaseEvent>();
            foreach (var row in rows)
            {
                var @event = CreateEventFromRow(row);
                if (@event != null)
                    events.Add(@event);
            }
            
            return events;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting events for aggregate {AggregateId}", aggregateId);
            return Enumerable.Empty<BaseEvent>();
        }
    }

    public async Task<IEnumerable<BaseEvent>> GetEventsFromVersionAsync(Guid aggregateId, int fromVersion)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            const string sql = @"
                SELECT event_id, event_type, aggregate_id, aggregate_type, version, event_data, metadata, source_id, created_at
                FROM base_event 
                WHERE aggregate_id = @AggregateId AND version >= @FromVersion
                ORDER BY version ASC";

            var rows = await connection.QueryAsync(sql, new { AggregateId = aggregateId, FromVersion = fromVersion });
            
            var events = new List<BaseEvent>();
            foreach (var row in rows)
            {
                var @event = CreateEventFromRow(row);
                if (@event != null)
                    events.Add(@event);
            }
            
            return events;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting events from version {FromVersion} for aggregate {AggregateId}", fromVersion, aggregateId);
            return Enumerable.Empty<BaseEvent>();
        }
    }

    public async Task<int> GetAggregateVersionAsync(Guid aggregateId)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            const string sql = "SELECT COALESCE(MAX(version), 0) FROM base_event WHERE aggregate_id = @AggregateId";
            
            var version = await connection.QuerySingleOrDefaultAsync<int>(sql, new { AggregateId = aggregateId });
            return version;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting version for aggregate {AggregateId}", aggregateId);
            return 0;
        }
    }

    public async Task<bool> AggregateExistsAsync(Guid aggregateId)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            const string sql = "SELECT 1 FROM base_event WHERE aggregate_id = @AggregateId LIMIT 1";
            
            var result = await connection.QuerySingleOrDefaultAsync<int?>(sql, new { AggregateId = aggregateId });
            return result.HasValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if aggregate {AggregateId} exists", aggregateId);
            return false;
        }
    }

    public async Task<IEnumerable<EventSource>> GetEventSourcesAsync()
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            const string sql = "SELECT source_id, source_type, source_name, description, configuration, created_at FROM event_source ORDER BY source_name";
            
            var rows = await connection.QueryAsync(sql);
            
            var sources = new List<EventSource>();
            foreach (var row in rows)
            {
                sources.Add(new EventSource
                {
                    SourceId = row.source_id,
                    SourceType = Enum.Parse<SourceType>(row.source_type, true),
                    SourceName = row.source_name,
                    Description = row.description,
                    Configuration = JsonDocument.Parse(row.configuration?.ToString() ?? "{}"),
                    CreatedAt = row.created_at
                });
            }
            
            return sources;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting event sources");
            return Enumerable.Empty<EventSource>();
        }
    }

    public async Task<EventSource?> GetEventSourceByNameAsync(string sourceName)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            const string sql = "SELECT source_id, source_type, source_name, description, configuration, created_at FROM event_source WHERE source_name = @SourceName";
            
            var row = await connection.QuerySingleOrDefaultAsync(sql, new { SourceName = sourceName });
            
            if (row == null) return null;
            
            return new EventSource
            {
                SourceId = row.source_id,
                SourceType = Enum.Parse<SourceType>(row.source_type, true),
                SourceName = row.source_name,
                Description = row.description,
                Configuration = JsonDocument.Parse(row.configuration?.ToString() ?? "{}"),
                CreatedAt = row.created_at
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting event source {SourceName}", sourceName);
            return null;
        }
    }

    public async Task<EventSource> CreateEventSourceAsync(string sourceName, SourceType sourceType, string? description = null)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            var eventSource = new EventSource
            {
                SourceId = Guid.NewGuid(),
                SourceName = sourceName,
                SourceType = sourceType,
                Description = description,
                Configuration = JsonDocument.Parse("{}"),
                CreatedAt = DateTime.UtcNow
            };
            
            const string sql = @"
                INSERT INTO event_source (source_id, source_type, source_name, description, configuration, created_at)
                VALUES (@SourceId, @SourceType, @SourceName, @Description, @Configuration::jsonb, @CreatedAt)";

            var parameters = new
            {
                eventSource.SourceId,
                SourceType = sourceType.ToString().ToLowerInvariant(),
                eventSource.SourceName,
                eventSource.Description,
                Configuration = eventSource.Configuration.RootElement.GetRawText(),
                eventSource.CreatedAt
            };

            await connection.ExecuteAsync(sql, parameters);
            return eventSource;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating event source {SourceName}", sourceName);
            throw;
        }
    }

    private BaseEvent? CreateEventFromRow(dynamic row)
    {
        try
        {
            // For now, create a generic event. In a real implementation,
            // you'd have a factory to create specific event types
            var genericEvent = new GenericEvent
            {
                EventId = row.event_id,
                AggregateId = row.aggregate_id,
                AggregateType = Enum.Parse<EntityType>((string)row.aggregate_type, true),
                Version = row.version,
                EventData = JsonDocument.Parse(row.event_data?.ToString() ?? "{}"),
                Metadata = JsonDocument.Parse(row.metadata?.ToString() ?? "{}"),
                SourceId = row.source_id,
                CreatedAt = row.created_at,
                EventTypeName = row.event_type
            };
            
            return genericEvent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating event from database row");
            return null;
        }
    }
}

/// <summary>
/// Generic event for events loaded from the database
/// </summary>
public class GenericEvent : BaseEvent
{
    public string EventTypeName { get; set; } = string.Empty;
    
    public override string EventType => EventTypeName;

    public override JsonDocument SerializeEventData()
    {
        return EventData;
    }

    public override void DeserializeEventData(JsonDocument eventData)
    {
        EventData = eventData;
    }
}
