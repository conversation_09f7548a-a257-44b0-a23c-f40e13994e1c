using System.Text.Json;
using Dapper;
using Microsoft.Extensions.Logging;
using TenantDB.Core.DataLayer;
using TenantDB.Core.EventSourcing.Models;

namespace TenantDB.Core.EventSourcing;

/// <summary>
/// Dapper-based implementation of the aggregate repository
/// </summary>
public class DapperAggregateRepository : IAggregateRepository
{
    private readonly IDataLayer _dataLayer;
    private readonly IEventStore _eventStore;
    private readonly ILogger<DapperAggregateRepository> _logger;

    public DapperAggregateRepository(IDataLayer dataLayer, IEventStore eventStore, ILogger<DapperAggregateRepository> logger)
    {
        _dataLayer = dataLayer ?? throw new ArgumentNullException(nameof(dataLayer));
        _eventStore = eventStore ?? throw new ArgumentNullException(nameof(eventStore));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<bool> SaveAsync(Aggregate aggregate)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            // First ensure the aggregate entity exists
            await EnsureAggregateEntityExistsAsync(aggregate, connection);
            
            // Then save/update the aggregate
            const string sql = @"
                INSERT INTO aggregate (id, entity_type, data, metadata, version, last_updated, created_at)
                VALUES (@Id, @EntityType, @Data::jsonb, @Metadata::jsonb, @Version, @LastUpdated, @CreatedAt)
                ON CONFLICT (id) DO UPDATE SET
                    data = EXCLUDED.data,
                    metadata = EXCLUDED.metadata,
                    version = EXCLUDED.version,
                    last_updated = EXCLUDED.last_updated";

            var parameters = new
            {
                aggregate.Id,
                EntityType = aggregate.EntityType.ToString().ToLowerInvariant(),
                Data = aggregate.GetState().RootElement.GetRawText(),
                Metadata = aggregate.Metadata.RootElement.GetRawText(),
                aggregate.Version,
                aggregate.LastUpdated,
                aggregate.CreatedAt
            };

            var result = await connection.ExecuteAsync(sql, parameters);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving aggregate {AggregateId}", aggregate.Id);
            return false;
        }
    }

    public async Task<TAggregate?> LoadAsync<TAggregate>(Guid aggregateId) where TAggregate : Aggregate, new()
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            const string sql = @"
                SELECT id, entity_type, data, metadata, version, last_updated, created_at
                FROM aggregate 
                WHERE id = @AggregateId";

            var row = await connection.QuerySingleOrDefaultAsync(sql, new { AggregateId = aggregateId });
            
            if (row == null) return null;
            
            var aggregate = new TAggregate
            {
                Id = row.id,
                Version = row.version,
                LastUpdated = row.last_updated,
                CreatedAt = row.created_at,
                Metadata = JsonDocument.Parse(row.metadata?.ToString() ?? "{}")
            };
            
            aggregate.SetState(JsonDocument.Parse(row.data?.ToString() ?? "{}"));
            
            return aggregate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading aggregate {AggregateId}", aggregateId);
            return null;
        }
    }

    public async Task<TAggregate?> LoadFromEventsAsync<TAggregate>(Guid aggregateId) where TAggregate : Aggregate, new()
    {
        try
        {
            var events = await _eventStore.GetEventsAsync(aggregateId);
            
            if (!events.Any()) return null;
            
            var aggregate = new TAggregate { Id = aggregateId };
            
            foreach (var @event in events.OrderBy(e => e.Version))
            {
                aggregate.Apply(@event);
            }
            
            return aggregate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading aggregate {AggregateId} from events", aggregateId);
            return null;
        }
    }

    public async Task<bool> ExistsAsync(Guid aggregateId)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            const string sql = "SELECT 1 FROM aggregate WHERE id = @AggregateId LIMIT 1";
            
            var result = await connection.QuerySingleOrDefaultAsync<int?>(sql, new { AggregateId = aggregateId });
            return result.HasValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if aggregate {AggregateId} exists", aggregateId);
            return false;
        }
    }

    public async Task<IEnumerable<TAggregate>> GetAllAsync<TAggregate>() where TAggregate : Aggregate, new()
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            var aggregate = new TAggregate();
            var entityType = aggregate.EntityType.ToString().ToLowerInvariant();
            
            const string sql = @"
                SELECT id, entity_type, data, metadata, version, last_updated, created_at
                FROM aggregate 
                WHERE entity_type = @EntityType
                ORDER BY created_at";

            var rows = await connection.QueryAsync(sql, new { EntityType = entityType });
            
            var aggregates = new List<TAggregate>();
            foreach (var row in rows)
            {
                var agg = new TAggregate
                {
                    Id = row.id,
                    Version = row.version,
                    LastUpdated = row.last_updated,
                    CreatedAt = row.created_at,
                    Metadata = JsonDocument.Parse(row.metadata?.ToString() ?? "{}")
                };
                
                agg.SetState(JsonDocument.Parse(row.data?.ToString() ?? "{}"));
                aggregates.Add(agg);
            }
            
            return aggregates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all aggregates of type {AggregateType}", typeof(TAggregate).Name);
            return Enumerable.Empty<TAggregate>();
        }
    }

    public async Task<bool> DeleteAsync(Guid aggregateId)
    {
        try
        {
            using var connection = await _dataLayer.GetConnectionAsync();
            
            // Delete from aggregate table (events remain for audit trail)
            const string sql = "DELETE FROM aggregate WHERE id = @AggregateId";
            
            var result = await connection.ExecuteAsync(sql, new { AggregateId = aggregateId });
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting aggregate {AggregateId}", aggregateId);
            return false;
        }
    }

    private async Task EnsureAggregateEntityExistsAsync(Aggregate aggregate, System.Data.IDbConnection connection)
    {
        const string checkSql = "SELECT 1 FROM aggregate_entity WHERE id = @Id LIMIT 1";
        var exists = await connection.QuerySingleOrDefaultAsync<int?>(checkSql, new { aggregate.Id });
        
        if (!exists.HasValue)
        {
            const string insertSql = @"
                INSERT INTO aggregate_entity (id, entity_type, created_at)
                VALUES (@Id, @EntityType, @CreatedAt)";
            
            var parameters = new
            {
                aggregate.Id,
                EntityType = aggregate.EntityType.ToString().ToLowerInvariant(),
                aggregate.CreatedAt
            };
            
            await connection.ExecuteAsync(insertSql, parameters);
        }
    }
}
