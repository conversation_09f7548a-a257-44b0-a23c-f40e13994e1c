using System.Text.Json;

namespace TenantDB.Core.EventSourcing.Models;

/// <summary>
/// Represents an event source in the system
/// </summary>
public class EventSource
{
    public Guid SourceId { get; set; }
    public SourceType SourceType { get; set; }
    public string SourceName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public JsonDocument Configuration { get; set; } = JsonDocument.Parse("{}");
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Source types for event sources
/// </summary>
public enum SourceType
{
    User,
    ComplianceService,
    Scraper,
    Aggregator,
    Campaign,
    Sms,
    Email,
    Api,
    System
}
