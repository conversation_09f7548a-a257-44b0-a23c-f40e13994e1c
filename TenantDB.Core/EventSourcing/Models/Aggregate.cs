using System.Text.Json;

namespace TenantDB.Core.EventSourcing.Models;

/// <summary>
/// Base aggregate class for event sourcing
/// </summary>
public abstract class Aggregate
{
    public Guid Id { get; set; }
    public abstract EntityType EntityType { get; }
    public JsonDocument Data { get; set; } = JsonDocument.Parse("{}");
    public JsonDocument Metadata { get; set; } = JsonDocument.Parse("{}");
    public int Version { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Apply an event to this aggregate
    /// </summary>
    public abstract void Apply(BaseEvent @event);

    /// <summary>
    /// Get the current state as a JSON document
    /// </summary>
    public abstract JsonDocument GetState();

    /// <summary>
    /// Set the state from a JSON document
    /// </summary>
    public abstract void SetState(JsonDocument state);
}

/// <summary>
/// Generic aggregate with typed state
/// </summary>
public abstract class Aggregate<TState> : Aggregate where TState : class, new()
{
    public TState State { get; set; } = new();

    public override JsonDocument GetState()
    {
        var json = JsonSerializer.Serialize(State);
        return JsonDocument.Parse(json);
    }

    public override void SetState(JsonDocument state)
    {
        var json = state.RootElement.GetRawText();
        State = JsonSerializer.Deserialize<TState>(json) ?? new TState();
    }

    /// <summary>
    /// Apply a typed event to this aggregate
    /// </summary>
    public abstract void Apply<TEventData>(BaseEvent<TEventData> @event) where TEventData : class, new();

    public override void Apply(BaseEvent @event)
    {
        // Try to call a strongly-typed overload: Apply(PersonCreatedEvent e), etc.
        var eventType = @event.GetType();
        var applyMethod = GetType().GetMethod(nameof(Apply), new[] { eventType });
        if (applyMethod != null)
        {
            applyMethod.Invoke(this, [@event]);
            return;
        }

        // Try to route to the generic typed Apply<TEventData>(BaseEvent<TEventData>) for BaseEvent<>, BaseEvent<,>
        var baseEventType = eventType.BaseType;
        if (baseEventType != null && baseEventType.IsGenericType)
        {
            var genericDef = baseEventType.GetGenericTypeDefinition();
            if (genericDef == typeof(BaseEvent<>) || genericDef == typeof(BaseEvent<,>))
            {
                // Find the generic method Apply<TEventData>(BaseEvent<TEventData>) on this aggregate
                var methods = GetType().GetMethods()
                    .Where(m => m.Name == nameof(Apply) && m.IsGenericMethodDefinition)
                    .ToList();
                var genericApply = methods.FirstOrDefault(m => m.GetParameters().Length == 1);
                if (genericApply != null)
                {
                    var typeArgs = baseEventType.GetGenericArguments();
                    // First generic argument is the event data type
                    var concreteApply = genericApply.MakeGenericMethod(typeArgs[0]);
                    concreteApply.Invoke(this, [@event]);
                    return;
                }
            }
        }

        // Fallback for untyped events
        ApplyUntyped(@event);
    }

    /// <summary>
    /// Apply an untyped event (fallback method)
    /// </summary>
    protected virtual void ApplyUntyped(BaseEvent @event)
    {
        // Default implementation - can be overridden
        Version = @event.Version;
        LastUpdated = @event.CreatedAt;
    }
}

/// <summary>
/// Entity types for aggregates
/// </summary>
public enum EntityType
{
    Person,
    Company,
    Website,
    Place
}
