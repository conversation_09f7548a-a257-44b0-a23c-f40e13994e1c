using System.Text.Json;

namespace TenantDB.Core.EventSourcing.Models;

/// <summary>
/// Base event class for all events in the system
/// </summary>
public abstract class BaseEvent
{
    public Guid EventId { get; set; } = Guid.NewGuid();
    public abstract string EventType { get; }
    public Guid AggregateId { get; set; }
    public EntityType AggregateType { get; set; }
    public int Version { get; set; }
    public JsonDocument EventData { get; set; } = JsonDocument.Parse("{}");
    public JsonDocument Metadata { get; set; } = JsonDocument.Parse("{}");
    public Guid SourceId { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Serialize the event data to JSON
    /// </summary>
    public abstract JsonDocument SerializeEventData();

    /// <summary>
    /// Deserialize event data from JSON
    /// </summary>
    public abstract void DeserializeEventData(JsonDocument eventData);

    /// <summary>
    /// Serialize the event metadata to JSON. Default implementation returns existing Metadata.
    /// </summary>
    public virtual JsonDocument SerializeMetadata()
    {
        return Metadata;
    }

    /// <summary>
    /// Deserialize event metadata from JSON. Default implementation assigns Metadata.
    /// </summary>
    public virtual void DeserializeMetadata(JsonDocument metadata)
    {
        Metadata = metadata;
    }
}

/// <summary>
/// Generic base event with typed event data
/// </summary>
public abstract class BaseEvent<TEventData> : BaseEvent where TEventData : class, new()
{
    public TEventData Data { get; set; } = new();

    public override JsonDocument SerializeEventData()
    {
        var json = JsonSerializer.Serialize(Data);
        return JsonDocument.Parse(json);
    }

    public override void DeserializeEventData(JsonDocument eventData)
    {
        var json = eventData.RootElement.GetRawText();
        Data = JsonSerializer.Deserialize<TEventData>(json) ?? new TEventData();
    }
}

/// <summary>
/// Generic base event with typed event data and typed metadata
/// </summary>
public abstract class BaseEvent<TEventData, TMetadata> : BaseEvent<TEventData>
    where TEventData : class, new()
    where TMetadata : class, new()
{
    public TMetadata Meta { get; set; } = new();

    public override JsonDocument SerializeMetadata()
    {
        var json = JsonSerializer.Serialize(Meta);
        return JsonDocument.Parse(json);
    }

    public override void DeserializeMetadata(JsonDocument metadata)
    {
        var json = metadata.RootElement.GetRawText();
        Meta = JsonSerializer.Deserialize<TMetadata>(json) ?? new TMetadata();
    }
}
