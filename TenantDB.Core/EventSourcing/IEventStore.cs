using TenantDB.Core.EventSourcing.Models;

namespace TenantDB.Core.EventSourcing;

/// <summary>
/// Interface for event store operations
/// </summary>
public interface IEventStore
{
    /// <summary>
    /// Save an event to the event store
    /// </summary>
    Task<bool> SaveEventAsync(BaseEvent @event);

    /// <summary>
    /// Get all events for an aggregate
    /// </summary>
    Task<IEnumerable<BaseEvent>> GetEventsAsync(Guid aggregateId);

    /// <summary>
    /// Get events for an aggregate from a specific version
    /// </summary>
    Task<IEnumerable<BaseEvent>> GetEventsFromVersionAsync(Guid aggregateId, int fromVersion);

    /// <summary>
    /// Get the current version of an aggregate
    /// </summary>
    Task<int> GetAggregateVersionAsync(Guid aggregateId);

    /// <summary>
    /// Check if an aggregate exists
    /// </summary>
    Task<bool> AggregateExistsAsync(Guid aggregateId);

    /// <summary>
    /// Get all event sources
    /// </summary>
    Task<IEnumerable<EventSource>> GetEventSourcesAsync();

    /// <summary>
    /// Get an event source by name
    /// </summary>
    Task<EventSource?> GetEventSourceByNameAsync(string sourceName);

    /// <summary>
    /// Create a new event source
    /// </summary>
    Task<EventSource> CreateEventSourceAsync(string sourceName, SourceType sourceType, string? description = null);
}
