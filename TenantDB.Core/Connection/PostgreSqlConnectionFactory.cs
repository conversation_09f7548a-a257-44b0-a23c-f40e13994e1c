using System.Data;
using Microsoft.Extensions.Logging;
using Npgsql;
using TenantDB.Core.Configuration;

namespace TenantDB.Core.Connection;

/// <summary>
/// PostgreSQL implementation of the connection factory
/// </summary>
public class PostgreSqlConnectionFactory : IConnectionFactory
{
    private readonly DatabaseConfiguration _configuration;
    private readonly ILogger<PostgreSqlConnectionFactory> _logger;

    public PostgreSqlConnectionFactory(
        DatabaseConfiguration configuration,
        ILogger<PostgreSqlConnectionFactory> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public async Task<IDbConnection> CreateConnectionAsync()
    {
        var connectionString = _configuration.BuildConnectionString();
        return await CreateConnectionInternalAsync(connectionString);
    }

    /// <inheritdoc />
    public async Task<IDbConnection> CreateConnectionAsync(string databaseName)
    {
        if (string.IsNullOrWhiteSpace(databaseName))
            throw new ArgumentException("Database name cannot be null or empty", nameof(databaseName));

        var connectionString = _configuration.BuildConnectionString(databaseName);
        return await CreateConnectionInternalAsync(connectionString);
    }

    /// <inheritdoc />
    public async Task<IDbConnection> CreateSystemConnectionAsync()
    {
        // Connect to the default 'postgres' database for administrative operations
        var connectionString = _configuration.BuildConnectionString("postgres");
        return await CreateConnectionInternalAsync(connectionString);
    }

    private async Task<IDbConnection> CreateConnectionInternalAsync(string connectionString)
    {
        try
        {
            _logger.LogDebug("Creating PostgreSQL connection");
            var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            _logger.LogDebug("PostgreSQL connection opened successfully");
            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create PostgreSQL connection");
            throw;
        }
    }
}
