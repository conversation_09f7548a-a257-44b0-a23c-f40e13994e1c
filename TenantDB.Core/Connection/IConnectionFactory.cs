using System.Data;

namespace TenantDB.Core.Connection;

/// <summary>
/// Factory interface for creating database connections
/// </summary>
public interface IConnectionFactory
{
    /// <summary>
    /// Creates a new database connection using the default database
    /// </summary>
    /// <returns>A new database connection</returns>
    Task<IDbConnection> CreateConnectionAsync();
    
    /// <summary>
    /// Creates a new database connection to a specific database
    /// </summary>
    /// <param name="databaseName">The name of the database to connect to</param>
    /// <returns>A new database connection</returns>
    Task<IDbConnection> CreateConnectionAsync(string databaseName);
    
    /// <summary>
    /// Creates a new database connection to the system database (for administrative operations)
    /// </summary>
    /// <returns>A new database connection to the system database</returns>
    Task<IDbConnection> CreateSystemConnectionAsync();
}
