﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
    <PackageReference Include="System.Text.Json" Version="9.0.7" />
  </ItemGroup>

</Project>
