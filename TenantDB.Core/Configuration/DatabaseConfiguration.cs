namespace TenantDB.Core.Configuration;

/// <summary>
/// Configuration settings for database connections
/// </summary>
public class DatabaseConfiguration
{
    public const string SectionName = "Database";
    
    /// <summary>
    /// PostgreSQL host address
    /// </summary>
    public string Host { get; set; } = "localhost";
    
    /// <summary>
    /// PostgreSQL port number
    /// </summary>
    public int Port { get; set; } = 5432;
    
    /// <summary>
    /// Database name
    /// </summary>
    public string Database { get; set; } = string.Empty;
    
    /// <summary>
    /// Database username
    /// </summary>
    public string Username { get; set; } = string.Empty;
    
    /// <summary>
    /// Database password
    /// </summary>
    public string Password { get; set; } = string.Empty;
    
    /// <summary>
    /// Connection timeout in seconds
    /// </summary>
    public int ConnectionTimeout { get; set; } = 30;
    
    /// <summary>
    /// Command timeout in seconds
    /// </summary>
    public int CommandTimeout { get; set; } = 30;
    
    /// <summary>
    /// Builds the connection string from the configuration
    /// </summary>
    /// <returns>PostgreSQL connection string</returns>
    public string BuildConnectionString()
    {
        return $"Host={Host};Port={Port};Database={Database};Username={Username};Password={Password};Timeout={ConnectionTimeout};Command Timeout={CommandTimeout}";
    }
    
    /// <summary>
    /// Builds a connection string for a specific database name
    /// </summary>
    /// <param name="databaseName">The database name to use</param>
    /// <returns>PostgreSQL connection string</returns>
    public string BuildConnectionString(string databaseName)
    {
        return $"Host={Host};Port={Port};Database={databaseName};Username={Username};Password={Password};Timeout={ConnectionTimeout};Command Timeout={CommandTimeout}";
    }
}
